import { IconAddressComponent } from '@/app/shared/icons/address/address.component';
import { IconAdministratorComponent } from '@/app/shared/icons/administrator/administrator.component';
import { IconArrowLeftComponent } from '@/app/shared/icons/arrow-left/arrow-left.component';
import { IconArrowRightComponent } from '@/app/shared/icons/arrow-right/arrow-right.component';
import { IconBlockedComponent } from '@/app/shared/icons/blocked/blocked.component';
import { IconBulletPointComponent } from '@/app/shared/icons/bulletpoint/bulletpoint.component';
import { IconBurgerMenuXsComponent } from '@/app/shared/icons/burger-menu-xs/burger-menu-xs.component';
import { IconCalendarComponent } from '@/app/shared/icons/calendar/calendar.component';
import { IconCancelComponent } from '@/app/shared/icons/cancel/cancel.component';
import { IconCertificateComponent } from '@/app/shared/icons/certificate/certificate.component';
import { IconCheckComponent } from '@/app/shared/icons/check/check.component';
import { IconChevronDownSmallComponent } from '@/app/shared/icons/chevron-down-small/chevron-down-small.component';
import { IconChevronDownComponent } from '@/app/shared/icons/chevron-down/chevron-down.component';
import { IconChevronLeftComponent } from '@/app/shared/icons/chevron-left/chevron-left.component';
import { IconChevronRightComponent } from '@/app/shared/icons/chevron-right/chevron-right.component';
import { IconChevronUpComponent } from '@/app/shared/icons/chevron-up/chevron-up.component';
import { IconContinueWithComponent } from '@/app/shared/icons/continue-with/continue-with.component';
import { IconDeleteComponent } from '@/app/shared/icons/delete/delete.component';
import { IconDigitalDocumentsComponent } from '@/app/shared/icons/digitaldocuments/digitaldocuments.component';
import { IconDigitalizeCardComponent } from '@/app/shared/icons/digitalize-card/digitalize-card.component';
import {
  IconDocumentCertificateComponent,
} from '@/app/shared/icons/document-certificate/document-certificate.component';
import { IconDocumentEmptyComponent } from '@/app/shared/icons/document-empty/document-empty.component';
import { IconDocumentPdfComponent } from '@/app/shared/icons/document-pdf/document-pdf.component';
import { IconEMailComponent } from '@/app/shared/icons/e-mail/e-mail.component';
import { IconEditComponent } from '@/app/shared/icons/edit/edit.component';
import { IconErrorComponent } from '@/app/shared/icons/error/error.component';
import { IconFishingTaxComponent } from '@/app/shared/icons/fisching-tax/fishing-tax.component';
import { IconForeignStateComponent } from '@/app/shared/icons/foreign-state/foreign-state.component';
import { IconHandicappedComponent } from '@/app/shared/icons/handicapped/handicapped.component';
import { IconInfoComponent } from '@/app/shared/icons/info/info.component';
import { IconLicenseCardComponent } from '@/app/shared/icons/license-card/license-card.component';
import { IconLinkComponent } from '@/app/shared/icons/link/link.component';
import { IconLockComponent } from '@/app/shared/icons/lock/lock.component';
import { IconLoginComponent } from '@/app/shared/icons/login/login.component';
import { IconMinusSmallComponent } from '@/app/shared/icons/minus-small/minus-small.component';
import { IconMoneyComponent } from '@/app/shared/icons/money/money.component';
import { IconMoveAuthorityComponent } from '@/app/shared/icons/move-authority/move-authority.component';
import { IconNfcComponent } from '@/app/shared/icons/nfc/nfc.component';
import { IconNoAddressComponent } from '@/app/shared/icons/no-address/no-address.component';
import { IconNoLicenseCardComponent } from '@/app/shared/icons/no-license-card/no-license-card.component';
import { IconOwnStateComponent } from '@/app/shared/icons/own-state/own-state.component';
import { IconPackageComponent } from '@/app/shared/icons/package/package.component';
import { IconPhoneCallComponent } from '@/app/shared/icons/phone-call/phone-call.component';
import { IconPlusSmallComponent } from '@/app/shared/icons/plus-small/plus-small.component';
import { IconPlusComponent } from '@/app/shared/icons/plus/plus.component';
import { IconPrintComponent } from '@/app/shared/icons/print/print.component';
import { IconQrComponent } from '@/app/shared/icons/qr/qr.component';
import { IconReturnComponent } from '@/app/shared/icons/return/return.component';
import { IconSearchComponent } from '@/app/shared/icons/search/search.component';
import { IconSendComponent } from '@/app/shared/icons/send/send.component';
import { IconStartpageComponent } from '@/app/shared/icons/startpage/startpage.component';
import { IconTimeSkipperLeftComponent } from '@/app/shared/icons/time-skipper-left/time-skipper-left.component';
import { IconTimeSkipperRightComponent } from '@/app/shared/icons/time-skipper-right/time-skipper-right.component';
import { IconUnlockComponent } from '@/app/shared/icons/unlock/unlock.component';
import { IconVacationComponent } from '@/app/shared/icons/vacation/vacation.component';
import { IconWarningComponent } from '@/app/shared/icons/warning/warning.component';
import { IconHomeComponent } from '@/app/shared/icons/home/<USER>';
import {
  IconWarningExclamationMarkComponent,
} from '@/app/shared/icons/warning-exclamation-mark/warning-exclamation-mark.component';
import { Component } from '@angular/core';
import { Meta, moduleMetadata, StoryObj } from '@storybook/angular';
import { IconNoTaxComponent } from '@/app/shared/icons/no-tax/no-tax.component';
import { IconAuthorityComponent } from '@/app/shared/icons/authority/authority.component';
import { IconDownloadComponent } from '@/app/shared/icons/download/download.component';
import {
  IconExaminationInstituteComponent,
} from '@/app/shared/icons/examination-institute/examination-institute.component';
import { IconNumberComponent } from '@/app/shared/icons/number/number.component';
import { IconLicenseApplication } from '@/app/shared/icons/license-application/license-application.component';
import {
  IconLicenseApplicationDenied,
} from '@/app/shared/icons/license-application-denied/license-application-denied.component';
import { IconStatisticComponent } from '@/app/shared/icons/statistic/statistic.component';
import { IconCashbookComponent } from '@/app/shared/icons/cashbook/cashbook.component';
import { IconControlInstituteComponent } from '@/app/shared/icons/control-institute/control-institute.component';

const ICON_COMPONENTS = [
  IconAddressComponent,
  IconAdministratorComponent,
  IconArrowLeftComponent,
  IconArrowRightComponent,
  IconAuthorityComponent,
  IconBlockedComponent,
  IconBulletPointComponent,
  IconBurgerMenuXsComponent,
  IconCalendarComponent,
  IconCashbookComponent,
  IconCancelComponent,
  IconCertificateComponent,
  IconChevronLeftComponent,
  IconCheckComponent,
  IconChevronDownComponent,
  IconChevronDownSmallComponent,
  IconChevronRightComponent,
  IconChevronUpComponent,
  IconContinueWithComponent,
  IconControlInstituteComponent,
  IconDeleteComponent,
  IconDigitalDocumentsComponent,
  IconDigitalizeCardComponent,
  IconDocumentCertificateComponent,
  IconDocumentEmptyComponent,
  IconDocumentPdfComponent,
  IconDownloadComponent,
  IconEMailComponent,
  IconEditComponent,
  IconErrorComponent,
  IconExaminationInstituteComponent,
  IconFishingTaxComponent,
  IconForeignStateComponent,
  IconInfoComponent,
  IconLicenseCardComponent,
  IconLinkComponent,
  IconLockComponent,
  IconLoginComponent,
  IconMinusSmallComponent,
  IconMoneyComponent,
  IconMoveAuthorityComponent,
  IconNfcComponent,
  IconNoAddressComponent,
  IconNoLicenseCardComponent,
  IconNumberComponent,
  IconOwnStateComponent,
  IconPackageComponent,
  IconPhoneCallComponent,
  IconPlusSmallComponent,
  IconPrintComponent,
  IconQrComponent,
  IconReturnComponent,
  IconSearchComponent,
  IconStatisticComponent,
  IconSendComponent,
  IconStartpageComponent,
  IconTimeSkipperLeftComponent,
  IconTimeSkipperRightComponent,
  IconUnlockComponent,
  IconVacationComponent,
  IconWarningComponent,
  IconHandicappedComponent,
  IconPlusComponent,
  IconHomeComponent,
  IconWarningExclamationMarkComponent,
  IconNoTaxComponent,
  IconLicenseApplication,
  IconLicenseApplicationDenied,
];

@Component({
  standalone: true,
  selector: 'fish-icons-story',
  template:
    '<div class="text-action-primary grid grid-cols-8 gap-4"><ng-content></ng-content></div>',
})
class IconStoryComponent {
}

const meta: Meta<IconStoryComponent> = {
  title: 'Icons',
  component: IconStoryComponent,
  decorators: [
    moduleMetadata({
      imports: ICON_COMPONENTS,
    }),
  ],
};

export default meta;

type Story = StoryObj<IconStoryComponent>;

const template16 =
  '<fish-icons-story>' +
  '<fish-icon-bulletpoint size="16" />' +
  '</fish-icons-story>';
export const Icons16: Story = {
  name: 'Icons 16px',
  render: () => ({
    template: template16,
  }),
};

const template32 =
  '<fish-icons-story>' +
  '<fish-icon-authority size="32" />' +
  '<fish-icon-continue-with size="32" />' +
  '<fish-icon-check size="32" />' +
  '<fish-icon-startpage size="32" />' +
  '<fish-icon-address size="32" />' +
  '<fish-icon-no-address size="32" />' +
  '<fish-icon-edit size="32" />' +
  '<fish-icon-license-card size="32" />' +
  '<fish-icon-cashbook size="32" />' +
  '<fish-icon-control-institute size="32" />' +
  '<fish-icon-certificate size="32" />' +
  '<fish-icon-move-authority size="32" />' +
  '<fish-icon-search size="32" />' +
  '<fish-icon-statistic size="32" />' +
  '<fish-icon-return size="32" />' +
  '<fish-icon-e-mail size="32" />' +
  '<fish-icon-examination-institute size="32" />' +
  '<fish-icon-delete size="32" />' +
  '<fish-icon-download size="32" />' +
  '<fish-icon-number [number]="1"  size="32" />' +
  '<fish-icon-number [number]="2"  size="32" />' +
  '<fish-icon-number [number]="3"  size="32" />' +
  '<fish-icon-number [number]="4"  size="32" />' +
  '<fish-icon-number [number]="5"  size="32" />' +
  '<fish-icon-own-state size="32" />' +
  '<fish-icon-lock size="32" />' +
  '<fish-icon-unlock size="32" />' +
  '<fish-icon-warning size="32" />' +
  '<fish-icon-chevron-right size="32" />' +
  '<fish-icon-chevron-left size="32" />' +
  '<fish-icon-print size="32" />' +
  '<fish-icon-send size="32" />' +
  '<fish-icon-bulletpoint size="32" />' +
  '<fish-icon-cancel size="32" />' +
  '<fish-icon-fishing-tax size="32" />' +
  '<fish-icon-handicapped size="32" />' +
  '<fish-icon-plus size="32" />' +
  '<fish-icon-vacation size="32" />' +
  '<fish-icon-calendar size="32" />' +
  '<fish-icon-qualifications-proof size="32" />' +
  '<fish-icon-grid size="32" />' +
  '<fish-icon-home size="32" />' +
  '<fish-icon-warning-exclamation-mark size="32" />' +
  '<fish-icon-info size="32" />' +
  '<fish-icon-license-application size="32" />' +
  '<fish-icon-license-application-denied size="32" />' +
  '</fish-icons-story>';

export const Icons32: Story = {
  name: 'Icons 32px',
  render: () => ({
    template: template32,
  }),
};

const template48 =
  '<fish-icons-story>' +
  '<fish-icon-fishing-tax size="48" />' +
  '<fish-icon-license-card size="48" />' +
  '<fish-icon-no-license-card size="48" />' +
  '<fish-icon-digitalize-card size="48" />' +
  '<fish-icon-login size="48" />' +
  '<fish-icon-document-empty size="48" />' +
  '<fish-icon-document-pdf size="48" />' +
  '<fish-icon-cashbook size="48" />' +
  '<fish-icon-chevron-up size="48" />' +
  '<fish-icon-vacation size="48" />' +
  '<fish-icon-search size="48" />' +
  '<fish-icon-statistic size="48" />' +
  '<fish-icon-cancel size="48" />' +
  '<fish-icon-delete size="48" />' +
  '<fish-icon-lock size="48" />' +
  '<fish-icon-own-state size="48" />' +
  '<fish-icon-foreign-state size="48" />' +
  '<fish-icon-administrator size="48" />' +
  '<fish-icon-link size="48" />' +
  '<fish-icon-arrow-right size="48" />' +
  '<fish-icon-arrow-left size="48" />' +
  '<fish-icon-startpage size="48" />' +
  '<fish-icon-check size="48" />' +
  '<fish-icon-chevron-down size="48" />' +
  '<fish-icon-chevron-right size="48" />' +
  '<fish-icon-chevron-left size="48" />' +
  '<fish-icon-edit size="48" />' +
  '<fish-icon-print size="48" />' +
  '<fish-icon-address size="48" />' +
  '<fish-icon-e-mail size="48" />' +
  '<fish-icon-chevron-down-small size="48" />' +
  '<fish-icon-send size="48" />' +
  '<fish-icon-info size="48" />' +
  '<fish-icon-package size="48" />' +
  '<fish-icon-calendar size="48" />' +
  '<fish-icon-time-skipper-left size="48" />' +
  '<fish-icon-time-skipper-right size="48" />' +
  '<fish-icon-minus-small size="48" />' +
  '<fish-icon-plus-small size="48" />' +
  '<fish-icon-burger-menu-xs size="48" />' +
  '<fish-icon-nfc size="48" />' +
  '<fish-icon-qr size="48" />' +
  '<fish-icon-phone-call size="48" />' +
  '<fish-icon-warning size="48" />' +
  '<fish-icon-error size="48" />' +
  '<fish-icon-blocked size="48" />' +
  '<fish-icon-handicapped size="48" />' +
  '<fish-icon-plus size="48" />' +
  '<fish-icon-home size="48" />' +
  '<fish-icon-warning-exclamation-mark size="48" />' +
  '<fish-icon-license-application size="48" />' +
  '</fish-icons-story>';

export const Icons48: Story = {
  name: 'Icons 48px',
  render: () => ({
    template: template48,
  }),
};

const template64 =
  '<fish-icons-story>' +
  '<fish-icon-certificate size="64" />' +
  '<fish-icon-document-empty size="64" />' +
  '<fish-icon-document-pdf size="64" />' +
  '<fish-icon-document-certificate size="64" />' +
  '<fish-icon-money size="64" />' +
  '<fish-icon-warning-exclamation-mark size="64" />' +
  '</fish-icons-story>';
export const Icons64: Story = {
  name: 'Icons 64px',
  render: () => ({
    template: template64,
  }),
};

const template96 =
  '<fish-icons-story>' +
  '<fish-icon-document-certificate size="96" />' +
  '<fish-icon-money size="96" />' +
  '<fish-icon-delete size="96" />' +
  '<fish-icon-fishing-tax size="96" />' +
  '<fish-icon-license-card size="96" />' +
  '<fish-icon-no-license-card size="96" />' +
  '<fish-icon-digitaldocuments size="96" />' +
  '<fish-icon-e-mail size="96" />' +
  '<fish-icon-move-authority size="96" />' +
  '<fish-icon-no-tax size="96" />' +
  '</fish-icons-story>';
export const Icons96: Story = {
  name: 'Icons 96px',
  render: () => ({
    template: template96,
  }),
};
