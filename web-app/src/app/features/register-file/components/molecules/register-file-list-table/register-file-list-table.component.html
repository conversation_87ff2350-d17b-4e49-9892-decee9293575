<table fishRegisterFileTable>
  <tr fishRegisterFileTableRow>
    @for (entry of Object.entries(data()[0]); track entry[0]) {
      <th fishRegisterFileTableHeader [innerText]="'register_file.filed_process.filed_process_data.' + category() + '.' + entry[0] | translate"></th>
    }
  </tr>
  @for (dataEntry of data(); track $index) {
    <tr fishRegisterFileTableRow>
      @for (entry of Object.entries(dataEntry); track entry[0]) {
        <td fishRegisterFileTableCell [innerText]="entry[1]"></td>
      }
    </tr>
  }
</table>
