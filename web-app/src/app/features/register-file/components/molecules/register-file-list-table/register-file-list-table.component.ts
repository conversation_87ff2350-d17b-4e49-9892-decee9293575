import { ChangeDetectionStrategy, Component, input } from '@angular/core';

import { TranslateModule } from '@ngx-translate/core';

import { FiledProcessData } from '@digifischdok/ngx-register-sdk';

import { RegisterFileTableCellDirective } from '@/app/features/register-file/directives/register-file-table-cell.directive';
import { RegisterFileTableHeaderDirective } from '@/app/features/register-file/directives/register-file-table-header.directive';
import { RegisterFileTableRowDirective } from '@/app/features/register-file/directives/register-file-table-row.component';
import { RegisterFileTableDirective } from '@/app/features/register-file/directives/register-file-table.directive';

@Component({
  selector: 'fish-register-file-list-table',
  imports: [
    RegisterFileTableDirective,
    RegisterFileTableRowDirective,
    RegisterFileTableHeaderDirective,
    RegisterFileTableCellDirective,
    TranslateModule,
  ],
  templateUrl: './register-file-list-table.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class RegisterFileListTableComponent {
  public readonly data = input.required<FiledProcessData[keyof FiledProcessData]>();

  public readonly category = input.required<keyof FiledProcessData>();

  //public readonly columns = input.required<string[]>();

  protected readonly Object = Object;
}
