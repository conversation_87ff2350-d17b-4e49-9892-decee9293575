import { DatePipe } from '@angular/common';
import { ChangeDetectionStrategy, Component, inject, signal } from '@angular/core';
import { ActivatedRoute } from '@angular/router';

import { TranslateModule } from '@ngx-translate/core';

import { RegisterFileResponse, RegisterFileService } from '@digifischdok/ngx-register-sdk';

import { RouteParamService } from '@/app/core/services/route-param.service';
import { RegisterFileListTableComponent } from '@/app/features/register-file/components/molecules/register-file-list-table/register-file-list-table.component';
import { RegisterFileTableCellDirective } from '@/app/features/register-file/directives/register-file-table-cell.directive';
import { RegisterFileTableHeaderDirective } from '@/app/features/register-file/directives/register-file-table-header.directive';
import { RegisterFileTableRowDirective } from '@/app/features/register-file/directives/register-file-table-row.component';
import { RegisterFileTableDirective } from '@/app/features/register-file/directives/register-file-table.directive';
import { LogoComponent } from '@/app/shared/atoms/logo/logo.component';

@Component({
  selector: 'fish-register-file-page',
  imports: [
    DatePipe,
    TranslateModule,
    RegisterFileTableDirective,
    RegisterFileTableRowDirective,
    RegisterFileTableHeaderDirective,
    RegisterFileTableCellDirective,
    LogoComponent,
    RegisterFileListTableComponent,
  ],
  templateUrl: './register-file-page.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class RegisterFilePageComponent {
  private readonly registerEntryId: string;

  // TODO: uncomment this when backend is implemented
  // protected readonly registerFile: Signal<RegisterFileResponse | undefined>;

  // TODO: remove this when backend is implemented
  protected readonly registerFile = signal<RegisterFileResponse>({
    registerEntryId: '********-1234-1234-1234-************',
    createdAt: new Date().toISOString(),
    createdByInstitution: 'Fischereibehörde Schleswig-Holstein',
    filedProcesses: [
      {
        actingInstitution: 'Comune X',
        federalStateOfInstitution: 'SH',
        processTimestamp: new Date().toISOString(),
        federalState: 'SH',
        issuedBy: 'Comune X',
        processType: 'QUALIFICATIONS_PROOF_CREATED',
        filedProcessData: {
          person: {
            firstname: 'Max',
            lastname: 'Mustermann',
            birthname: '',
            birthplace: 'Musterstadt',
            birthdate: '01.01.2000',
            email: '<EMAIL>',
            address: {
              office: '',
              deliverTo: '',
              street: 'Musterstraße',
              streetNumber: '1',
              postcode: '12345',
              city: 'Musterstadt',
              detail: '',
            },
            serviceAccountId: '********-1234-1234-1234-************',
            serviceAccountLink: 'https://comune-x.de',
          },
          qualificationsProof: {
            type: 'CERTIFICATE',
            fishingCertificateId: '****************',
            federalState: 'SH',
            passedOn: new Date().toISOString(),
            issuedBy: 'Comune X',
          },
        },
      },
      {
        actingInstitution: 'Comune X',
        federalStateOfInstitution: 'SH',
        processTimestamp: new Date().toISOString(),
        federalState: 'SH',
        issuedBy: 'Comune X',
        processType: 'JURISDICTION_CHANGED',
        filedProcessData: {
          taxes: [
            {
              taxId: '********-1234-1234-1234-************',
              federalState: 'SH',
              validFrom: new Date().toISOString(),
              validTo: undefined,
              paymentInfo: {
                amount: 100,
                type: 'CASH',
              },
            },
          ],
          consentInfo: {
            gdprAccepted: true,
            submittedByThirdParty: false,
            selfDisclosureAccepted: true,
            proofOfMoveVerified: true,
          },
          // @ts-expect-error wrong openapi
          jurisdiction: {
            federalState: 'SH',
          },
          identificationDocuments: [
            {
              fishingTaxId: '********-1234-1234-1234-************',
              fishingLicenseId: '',
              issuedDate: new Date().toISOString(),
              type: 'PDF',
              validFrom: new Date().toISOString(),
              validTo: '',
            },
          ],
        },
      },
      {
        actingInstitution: 'Comune X',
        federalStateOfInstitution: 'SH',
        processTimestamp: new Date().toISOString(),
        federalState: 'SH',
        issuedBy: 'Comune X',
        processType: 'FISHING_LICENSE_CREATED',
        filedProcessData: {
          fees: [
            {
              federalState: 'SH',
              validFrom: new Date().toISOString(),
              validTo: undefined,
              paymentInfo: {
                amount: 100,
                type: 'CASH',
              },
            },
          ],
          person: {
            firstname: 'Max',
            lastname: 'Mustermann',
            birthname: '',
            birthplace: 'Musterstadt',
            birthdate: '01.01.2000',
            email: '<EMAIL>',
            officeAddress: {
              office: 'Musteramt',
              deliverTo: 'Max Mustermann',
              street: 'Musterstraße',
              streetNumber: '1',
              postcode: '12345',
              city: 'Musterstadt',
              detail: '',
            },
          },
          fishingLicense: {
            number: 'SH00000000000000',
            issuingFederalState: 'SH',
            type: 'REGULAR',
            validityPeriods: [
              {
                validFrom: new Date().toISOString(),
                validTo: undefined,
              },
            ],
          },
        },
      },
      {
        actingInstitution: 'Comune X',
        federalStateOfInstitution: 'SH',
        processTimestamp: new Date().toISOString(),
        federalState: 'SH',
        issuedBy: 'Comune X',
        processType: 'FISHING_TAX_CREATED',
        filedProcessData: {
          taxes: [
            {
              taxId: '********-1234-1234-1234-************',
              federalState: 'SH',
              validFrom: new Date().toISOString(),
              validTo: undefined,
              paymentInfo: {
                amount: 100,
                type: 'CASH',
              },
            },
          ],
          consentInfo: {
            gdprAccepted: true,
            submittedByThirdParty: false,
            selfDisclosureAccepted: true,
          },
          identificationDocuments: [
            {
              fishingTaxId: '********-1234-1234-1234-************',
              fishingLicenseId: '',
              issuedDate: new Date().toISOString(),
              type: 'PDF',
              validFrom: new Date().toISOString(),
              validTo: '',
            },
          ],
        },
      },
      {
        actingInstitution: 'Comune X',
        federalStateOfInstitution: 'SH',
        processTimestamp: new Date().toISOString(),
        federalState: 'SH',
        issuedBy: 'Comune X',
        processType: 'REPLACEMENT_CARD_ORDERED',
        filedProcessData: {
          person: {
            firstname: 'Max',
            lastname: 'Mustermann',
            birthname: '',
            birthplace: 'Musterstadt',
            birthdate: '01.01.2000',
            email: '<EMAIL>',
          },
          fishingLicense: {
            number: 'SH00000000000000',
            issuingFederalState: 'SH',
            type: 'REGULAR',
            validityPeriods: [
              {
                validFrom: new Date().toISOString(),
                validTo: undefined,
              },
            ],
          },
        },
      },
      {
        actingInstitution: 'Comune X',
        federalStateOfInstitution: 'SH',
        processTimestamp: new Date().toISOString(),
        federalState: 'SH',
        issuedBy: 'Comune X',
        processType: 'JURISDICTION_CHANGED',
        filedProcessData: {
          person: {
            firstname: 'Max',
            lastname: 'Mustermann',
            birthname: '',
            birthplace: 'Musterstadt',
            birthdate: '01.01.2000',
            email: '<EMAIL>',
          },
        },
      },
      {
        actingInstitution: 'Comune X',
        federalStateOfInstitution: 'SH',
        processTimestamp: new Date().toISOString(),
        federalState: 'SH',
        issuedBy: 'Comune X',
        processType: 'BANNED',
        filedProcessData: {
          person: {
            firstname: 'Max',
            lastname: 'Mustermann',
            birthname: '',
            birthplace: 'Musterstadt',
            birthdate: '01.01.2000',
            email: '<EMAIL>',
          },
          ban: {
            fileNumber: '********-1234-1234-1234-************',
            reportedBy: 'Comune X',
            at: new Date().toISOString(),
            from: new Date().toISOString(),
            to: undefined,
          },
        },
      },
      {
        actingInstitution: 'Comune X',
        federalStateOfInstitution: 'SH',
        processTimestamp: new Date().toISOString(),
        federalState: 'SH',
        issuedBy: 'Comune X',
        processType: 'UNBANNED',
        filedProcessData: {
          person: {
            firstname: 'Max',
            lastname: 'Mustermann',
            birthname: '',
            birthplace: 'Musterstadt',
            birthdate: '01.01.2000',
            email: '<EMAIL>',
          },
          ban: {
            fileNumber: '********-1234-1234-1234-************',
            reportedBy: 'Comune X',
            at: new Date().toISOString(),
            from: new Date().toISOString(),
            to: undefined,
          },
        },
      },
    ],
  });

  private readonly route = inject(ActivatedRoute);

  private readonly routeParamService = inject(RouteParamService);

  private readonly registerFileService = inject(RegisterFileService);

  constructor() {
    this.registerEntryId = this.routeParamService.getParamOrFail('registerEntryId', this.route);

    // TODO: uncomment this when backend is implemented
    // this.registerFile = toSignal<RegisterFileResponse>(this.registerFileService.registerFileControllerGet(this.registerEntryId));
  }

  protected readonly Object = Object;
}
