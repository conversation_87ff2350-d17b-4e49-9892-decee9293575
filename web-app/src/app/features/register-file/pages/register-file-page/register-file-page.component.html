<div class="mx-auto max-w-[800px] pt-12 print:pt-0">
  <div class="flex items-center justify-between">
    <fish-logo size="m" />
    <h1 [innerText]="'Registerauszug'" class="text-l font-bold text-action-primary"></h1>
  </div>
  <table class="mb-20 ml-auto mt-12 text-xs">
    <tr>
      <th class="text-left">Register-ID:</th>
      <td class="text-right" [innerText]="registerFile().registerEntryId"></td>
    </tr>
    <tr>
      <th class="text-left">Auszug erstellt am:</th>
      <td class="text-right" [innerText]="registerFile().createdAt | date: 'dd.MM.yyyy, HH:mm:ss \'Uhr\''"></td>
    </tr>
    <tr>
      <th class="text-left">Auszug erstellt von:</th>
      <td class="text-right" [innerText]="registerFile().createdByInstitution"></td>
    </tr>
  </table>
  <div class="flex flex-col gap-10">
    @for (process of registerFile().filedProcesses; track process.processTimestamp) {
      <div>
        <h2
          [innerText]="'Vorgang: ' + ('register_file.filed_process.process_type.' + process.processType.toLowerCase() | translate)"
          class="mb-4 mt-2 text-base text-action-primary"
        ></h2>
        <h3 [innerText]="'Vorgangskopfdaten'" class="mb-2 mt-6 font-bold"></h3>
        <table fishRegisterFileTable>
          <tr fishRegisterFileTableRow>
            <th fishRegisterFileTableHeader [innerText]="'Feld'"></th>
            <th fishRegisterFileTableHeader [innerText]="'Wert'"></th>
          </tr>
          <tr fishRegisterFileTableRow>
            <td fishRegisterFileTableCell [innerText]="'register_file.filed_process.actingInstitution' | translate"></td>
            <td fishRegisterFileTableCell [innerText]="process.actingInstitution"></td>
          </tr>
          <tr fishRegisterFileTableRow>
            <td fishRegisterFileTableCell [innerText]="'register_file.filed_process.federalStateOfInstitution' | translate"></td>
            <td fishRegisterFileTableCell [innerText]="process.federalStateOfInstitution"></td>
          </tr>
          <tr fishRegisterFileTableRow>
            <td fishRegisterFileTableCell [innerText]="'register_file.filed_process.processTimestamp' | translate"></td>
            <td fishRegisterFileTableCell [innerText]="process.processTimestamp"></td>
          </tr>
        </table>
        @if (process.filedProcessData.person) {
          <h3 [innerText]="'Personendaten'" class="mb-2 mt-6 font-bold"></h3>
          <table fishRegisterFileTable>
            <tr fishRegisterFileTableRow>
              <th fishRegisterFileTableHeader [innerText]="'Feld'"></th>
              <th fishRegisterFileTableHeader [innerText]="'Wert'"></th>
            </tr>
            @for (entry of Object.entries(process.filedProcessData.person); track entry[0]) {
              <tr fishRegisterFileTableRow>
                <td fishRegisterFileTableCell [innerText]="'register_file.filed_process.filed_process_data.person.' + entry[0] | translate"></td>
                <td fishRegisterFileTableCell [innerText]="entry[1]"></td>
              </tr>
            }
          </table>
        }
        @if (process.filedProcessData.person?.officeAddress) {
          <h3 [innerText]="'Behördenadresse'" class="mb-2 mt-6 font-bold"></h3>
          <table fishRegisterFileTable>
            <tr fishRegisterFileTableRow>
              <th fishRegisterFileTableHeader [innerText]="'Feld'"></th>
              <th fishRegisterFileTableHeader [innerText]="'Wert'"></th>
            </tr>
            @for (entry of Object.entries(process.filedProcessData.person!.officeAddress!); track entry[0]) {
              <tr fishRegisterFileTableRow>
                <td
                  fishRegisterFileTableCell
                  [innerText]="'register_file.filed_process.filed_process_data.officeAddress.' + entry[0] | translate"
                ></td>
                <td fishRegisterFileTableCell [innerText]="entry[1]"></td>
              </tr>
            }
          </table>
        }
        @if (process.filedProcessData.qualificationsProof) {
          <h3 [innerText]="'Nachweise'" class="mb-2 mt-6 font-bold"></h3>
          <table fishRegisterFileTable>
            <tr fishRegisterFileTableRow>
              <th fishRegisterFileTableHeader [innerText]="'Feld'"></th>
              <th fishRegisterFileTableHeader [innerText]="'Wert'"></th>
            </tr>
            @for (entry of Object.entries(process.filedProcessData.qualificationsProof); track entry[0]) {
              <tr fishRegisterFileTableRow>
                <td
                  fishRegisterFileTableCell
                  [innerText]="'register_file.filed_process.filed_process_data.qualificationsProof.' + entry[0] | translate"
                ></td>
                <td fishRegisterFileTableCell [innerText]="entry[1]"></td>
              </tr>
            }
          </table>
        }
        @if (process.filedProcessData.fees && process.filedProcessData.fees.length > 0) {
          <h3 [innerText]="'Gebühren'" class="mb-2 mt-6 font-bold"></h3>
          <fish-register-file-list-table [category]="'fees'" [data]="process.filedProcessData.fees"></fish-register-file-list-table>
        }
        @if (process.filedProcessData.taxes && process.filedProcessData.taxes.length > 0) {
          <h3 [innerText]="'Fischereiabgaben'" class="mb-2 mt-6 font-bold"></h3>
          <fish-register-file-list-table [category]="'taxes'" [data]="process.filedProcessData.taxes"></fish-register-file-list-table>
        }
        @if (process.filedProcessData.fishingLicense) {
          <h3 [innerText]="'Nachweise'" class="mb-2 mt-6 font-bold"></h3>
          <table fishRegisterFileTable>
            <tr fishRegisterFileTableRow>
              <th fishRegisterFileTableHeader [innerText]="'Feld'"></th>
              <th fishRegisterFileTableHeader [innerText]="'Wert'"></th>
            </tr>
            @for (entry of Object.entries(process.filedProcessData.fishingLicense); track entry[0]) {
              <tr fishRegisterFileTableRow>
                <td
                  fishRegisterFileTableCell
                  [innerText]="'register_file.filed_process.filed_process_data.qualificationsProof.' + entry[0] | translate"
                ></td>
                <td fishRegisterFileTableCell [innerText]="entry[1]"></td>
              </tr>
            }
          </table>
        }
        @if (process.filedProcessData.consentInfo) {
          <h3 [innerText]="'Zustimmungen'" class="mb-2 mt-6 font-bold"></h3>
          <table fishRegisterFileTable>
            <tr fishRegisterFileTableRow>
              <th fishRegisterFileTableHeader [innerText]="'Feld'"></th>
              <th fishRegisterFileTableHeader [innerText]="'Wert'"></th>
            </tr>
            @for (entry of Object.entries(process.filedProcessData.consentInfo); track entry[0]) {
              <tr fishRegisterFileTableRow>
                <td fishRegisterFileTableCell [innerText]="'register_file.filed_process.filed_process_data.consentInfo.' + entry[0] | translate"></td>
                <td fishRegisterFileTableCell [innerText]="'register_file.filed_process.filed_process_data.consentInfo.' + entry[1] | translate"></td>
              </tr>
            }
          </table>
        }
        @if ($any(process.filedProcessData).jurisdiction) {
          <h3 [innerText]="'Zuständigkeit'" class="mb-2 mt-6 font-bold"></h3>
          <table fishRegisterFileTable>
            <tr fishRegisterFileTableRow>
              <th fishRegisterFileTableHeader [innerText]="'Feld'"></th>
              <th fishRegisterFileTableHeader [innerText]="'Wert'"></th>
            </tr>
            <tr fishRegisterFileTableRow>
              <td fishRegisterFileTableCell [innerText]="'register_file.filed_process.filed_process_data.jurisdiction.federalState' | translate"></td>
              <td fishRegisterFileTableCell [innerText]="$any(process.filedProcessData).jurisdiction.federalState"></td>
            </tr>
          </table>
        }
        @if (process.filedProcessData.identificationDocuments && process.filedProcessData.identificationDocuments.length > 0) {
          <h3 [innerText]="'Identifikationsdokumente'" class="mb-2 mt-6 font-bold"></h3>
          <fish-register-file-list-table
            [category]="'identificationDocuments'"
            [data]="process.filedProcessData.identificationDocuments"
          ></fish-register-file-list-table>
        }
      </div>
    }
  </div>
</div>
